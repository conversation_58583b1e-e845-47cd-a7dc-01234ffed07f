import React, { useState, useEffect, useContext } from "react";
import { GlobalContext, showToast } from "Context/Global";
import MkdSDK from "Utils/MkdSDK";
import { useClub } from "Context/Club";
import { BackButton } from "Components/BackButton";
import { fCurrency } from "Utils/formatNumber";
import { InteractiveButton } from "Components/InteractiveButton";
import { Link } from "react-router-dom";
import TreeSDK from "Utils/TreeSDK";
import { useNavigate } from "react-router-dom";
import LoadingSpinner from "Components/LoadingSpinner";
import {
  actionLogTypes,
  activityLogTypes,
  getTimeRange,
  logActivity,
  reservationTypes,
  updateBrowserTab,
  getCourtPrice,
} from "Utils/utils";
import TimeSlots from "Components/TimeSlots/TimeSlots";
import { AuthContext } from "Context/Auth";
import AddPlayers from "Components/Players/AddPlayers";
import { calculateServiceFee } from "Utils/utils";
import Calendar from "Components/Calendar/Calendar";
import CheckoutForm from "Components/PaymentForm";
import SportTypeSelection from "Components/Shared/SportTypeSelection";
import { CourtReservationSummary } from "Components/Reservation/ReservationSummary";
import moment from "moment";
import WarningModal from "Components/Modals/WarningModal";
import Select from "react-select";

let sdk = new MkdSDK();
let tdk = new TreeSDK();

const UserReserveCourt = ({}) => {
  const [selectedSport, setSelectedSport] = useState(null);
  const [selectedDate, setSelectedDate] = useState(null);
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [players, setPlayers] = useState([]);
  const [groups, setGroups] = useState([]);
  const [fee, setFee] = useState(0.0);
  const [totalFee, setTotalFee] = useState(0.0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentView, setCurrentView] = useState("main");
  const [selectedPlayers, setSelectedPlayers] = useState([]);
  const [isFindBuddyEnabled, setIsFindBuddyEnabled] = useState(false);
  const [playersNeeded, setPlayersNeeded] = useState(1);
  const [showPlayersNeeded, setShowPlayersNeeded] = useState(false);
  const [ntrpMin, setNtrpMin] = useState(3.5);
  const [ntrpMax, setNtrpMax] = useState(3.5);
  const [shortBio, setShortBio] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [selectedType, setSelectedType] = useState(null);
  const [selectedSubType, setSelectedSubType] = useState(null);
  const [selectedTimes, setSelectedTimes] = useState([]);
  const [filteredCourts, setFilteredCourts] = useState([]);
  const [selectedCourt, setSelectedCourt] = useState(null);
  const [clientSecret, setClientSecret] = useState(null);
  const [paymentIntent, setPaymentIntent] = useState(null);
  const [paymentIntentLoading, setPaymentIntentLoading] = useState(false);
  const [bookingId, setBookingId] = useState(null);
  const [reservationId, setReservationId] = useState(null);
  const [warningModal, setWarningModal] = useState({
    isOpen: false,
    title: "",
    message: "",
    actionButtonText: "",
    actionButtonLink: "",
    type: "warning",
  });

  const { state: globalState, dispatch: globalDispatch } =
    useContext(GlobalContext);
  const { state: authState, dispatch: authDispatch } = useContext(AuthContext);
  const {
    club,
    pricing,
    sports,
    loading: clubLoading,
    user_subscription,
    user_permissions,
    user_profile,
    club_membership,
    courts,
  } = useClub();
  const navigate = useNavigate();
  console.log("club", club);
  const user_id = localStorage.getItem("user");

  // Get the user's membership plan
  const userMembershipPlan = React.useMemo(() => {
    if (!user_subscription?.planId || !club_membership?.length) return null;
    return club_membership.find(
      (plan) => plan.plan_id === user_subscription.planId
    );
  }, [user_subscription, club_membership]);
  // Calculate the maximum allowed date based on the user's membership plan
  const maxAllowedDate = React.useMemo(() => {
    // Check if advance booking is enabled for courts
    if (userMembershipPlan?.advance_booking_enabled?.court === false) {
      // If advance booking is disabled, allow booking without limit
      const farFutureDate = new Date();
      farFutureDate.setFullYear(farFutureDate.getFullYear() + 10); // Set to 10 years in the future
      return farFutureDate;
    }

    // Default to 10 days in advance if no plan or advance booking days are specified
    const advanceDays = userMembershipPlan?.advance_booking_days?.court || 10;
    const today = new Date();
    const maxDate = new Date();
    maxDate.setDate(today.getDate() + advanceDays);
    return maxDate;
  }, [userMembershipPlan]);

  // Function to check if a date is selectable based on membership plan
  const isDateSelectable = (date) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Don't allow dates in the past
    if (date < today) return false;

    // Don't allow dates beyond the max allowed date
    if (date > maxAllowedDate) return false;

    return true;
  };

  const fetchPlayers = async () => {
    try {
      const playersResponse = await tdk.getList("user", {
        filter: [`role,cs,user`, `club_id,cs,${club?.id}`],
      });
      setPlayers(playersResponse.list);
    } catch (error) {
      console.error(error);
    }
  };

  const fetchGroups = async () => {
    try {
      const groupsResponse = await sdk.callRawAPI(
        "/v3/api/custom/courtmatchup/user/groups",
        {},
        "GET"
      );
      setGroups(groupsResponse.groups);
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    (async () => {
      setIsLoading(true);
      // await fetchUserAndPlayers();

      await fetchGroups();
      setIsLoading(false);
    })();
  }, []);

  useEffect(() => {
    fetchPlayers();
  }, [club?.id]);

  React.useEffect(() => {
    updateBrowserTab({
      path: "/user/reserve-court",
      clubName: club?.name,
      favicon: club?.club_logo,
      description: "Reserve a court",
    });
  }, [club?.club_logo]);

  useEffect(() => {
    if (currentView === "players") {
    }
  }, [currentView]);

  const handlePreviousMonth = () => {
    setCurrentMonth(
      new Date(currentMonth.setMonth(currentMonth.getMonth() - 1))
    );
  };

  const handleNextMonth = () => {
    setCurrentMonth(
      new Date(currentMonth.setMonth(currentMonth.getMonth() + 1))
    );
  };

  // Get the selected sport's types and subtypes
  const selectedSportData = sports?.find((s) => s.id === selectedSport);

  // Parse custom request threshold data
  const parseCustomRequestThreshold = () => {
    try {
      if (!club?.custom_request_threshold) return [];

      // If it's already an object, return it
      if (typeof club.custom_request_threshold === "object") {
        return club.custom_request_threshold;
      }

      // Parse the JSON string
      return JSON.parse(club.custom_request_threshold);
    } catch (error) {
      console.error("Error parsing custom request threshold:", error);
      return [];
    }
  };

  // Get threshold based on selected sport, type, and subtype
  const getCustomRequestThreshold = () => {
    try {
      const thresholdData = parseCustomRequestThreshold();

      // Default threshold if nothing is found
      const defaultThreshold = 4;

      if (!Array.isArray(thresholdData) || !selectedSport) {
        return defaultThreshold;
      }

      // Find the selected sport in the threshold data
      const sportThreshold = thresholdData.find(
        (sport) => sport.id.toString() === selectedSport.toString()
      );

      if (!sportThreshold) {
        return defaultThreshold;
      }

      // If type is selected, check for type-specific threshold
      if (selectedType && sportThreshold.sport_types) {
        const typeThreshold = sportThreshold.sport_types.find(
          (type) => type.type === selectedType
        );

        if (typeThreshold && typeThreshold.threshold > 0) {
          return typeThreshold.threshold;
        }
      }

      // If no type-specific threshold or type not selected, use sport threshold
      return sportThreshold.threshold > 0
        ? sportThreshold.threshold
        : defaultThreshold;
    } catch (error) {
      console.error("Error getting custom request threshold:", error);
      return 4; // Default threshold
    }
  };

  // Get the maximum players allowed based on custom request threshold
  const maxPlayersAllowed = getCustomRequestThreshold();

  // Log threshold data for debugging
  useEffect(() => {
    if (selectedSport) {
      console.log("Selected Sport:", selectedSport);
      console.log("Selected Type:", selectedType);
      console.log("Selected SubType:", selectedSubType);
      console.log("Max Players Allowed:", maxPlayersAllowed);
      console.log(
        "Custom Request Threshold Data:",
        parseCustomRequestThreshold()
      );
    }
  }, [selectedSport, selectedType, selectedSubType, maxPlayersAllowed]);

  // Update playersNeeded when maxPlayersAllowed changes
  useEffect(() => {
    // Ensure playersNeeded doesn't exceed the new maximum
    if (playersNeeded > maxPlayersAllowed - selectedPlayers.length) {
      setPlayersNeeded(Math.max(0, maxPlayersAllowed - selectedPlayers.length));
    }
  }, [maxPlayersAllowed, selectedPlayers.length]);

  const { start_time, end_time, duration } = getTimeRange(selectedTimes);

  // Filter courts based on selected sport, type, and sub-type
  useEffect(() => {
    if (courts && courts.length > 0) {
      let filtered = [...courts];

      // Filter by sport_id if selected
      if (selectedSport) {
        filtered = filtered.filter(
          (court) =>
            court.sport_id &&
            court.sport_id.toString() === selectedSport.toString()
        );
      }

      // Filter by type if selected
      if (selectedType) {
        filtered = filtered.filter((court) => court.type === selectedType);
      }

      // Filter by sub_type if selected
      if (selectedSubType) {
        filtered = filtered.filter(
          (court) => court.sub_type === selectedSubType
        );
      }

      // Filter by allow_reservation setting in court_settings
      filtered = filtered.filter((court) => {
        try {
          // If court_settings is null or empty, default to allowing reservations
          if (!court.court_settings) {
            return true;
          }

          // Parse the court_settings JSON string
          const courtSettings = JSON.parse(court.court_settings);

          // Check if allow_reservation is explicitly set to false
          return courtSettings.allow_reservation !== false;
        } catch (error) {
          // If JSON parsing fails, default to allowing reservations for backward compatibility
          console.warn(
            `Failed to parse court_settings for court ${court.id}:`,
            error
          );
          return true;
        }
      });

      setFilteredCourts(filtered);
    } else {
      setFilteredCourts([]);
    }
  }, [courts, selectedSport, selectedType, selectedSubType]);

  const handleCreateBooking = async () => {
    // Check if sport has types and subtypes
    const sportHasTypes = selectedSportData?.sport_types?.some(
      (t) => t.type && t.type.trim() !== ""
    );
    const selectedTypeData = selectedSportData?.sport_types?.find(
      (t) => t.type === selectedType
    );
    const typeHasSubtypes = selectedTypeData?.subtype?.some(
      (st) => st && st.trim() !== ""
    );

    // Validate based on what the sport requires
    if (
      !selectedSport ||
      (sportHasTypes && !selectedType) ||
      (sportHasTypes && typeHasSubtypes && !selectedSubType) ||
      !selectedDate ||
      !start_time ||
      !end_time
    ) {
      showToast(
        globalDispatch,
        "Please select all required fields",
        3000,
        "error"
      );
      return;
    }

    setIsSubmitting(true);
    try {
      const paymentIntentResponse = await sdk.callRawAPI(
        "/v3/api/custom/courtmatchup/user/reservations/payment-intent/create",
        { amount: totalFee },
        "POST"
      );

      setClientSecret(paymentIntentResponse.client_secret);
      setPaymentIntent(paymentIntentResponse.payment_intent);

      // Use moment to format the date in local timezone
      const formattedDate = moment(selectedDate).format("YYYY-MM-DD");

      const payload = {
        sport_id: selectedSport,
        type: selectedType,
        sub_type: selectedSubType,
        date: formattedDate,
        start_time: start_time,
        end_time: end_time,
        duration: duration,
        reservation_type: reservationTypes.court,
        price: totalFee,
        player_ids: selectedPlayers.map((p) => p.id),
        buddy_details: null,
        payment_status: 0,
        payment_intent: paymentIntentResponse.payment_intent,
        service_fee: calculateServiceFee(club?.fee_settings, fee),
        club_fee: club?.club_fee,
        players_needed: playersNeeded,
        min_ntrp: ntrpMin,
        max_ntrp: ntrpMax,
        note: shortBio,
      };

      // Add court_id to payload if user court selection is allowed and a court is selected
      if (club?.allow_user_court_selection === 1 && selectedCourt) {
        payload.court_id = selectedCourt;
      }

      const response = await sdk.callRawAPI(
        "/v3/api/custom/courtmatchup/user/reservations",
        payload,
        "POST"
      );

      // Create activity log
      await logActivity(sdk, {
        user_id: localStorage.getItem("user"),
        activity_type: activityLogTypes.court_reservation,
        action_type: actionLogTypes.CREATE,
        data: payload,
        club_id: club?.id,
        description: `${user_profile?.first_name} ${user_profile?.last_name} created a court reservation`,
      });

      if (!response.error) {
        showToast(
          globalDispatch,
          "Reservation created successfully",
          3000,
          "success"
        );
        // navigate("/user/dashboard"); // Navigate to reservations list
      }
      setReservationId(response.reservation_id);
      return response.booking_id;
    } catch (error) {
      console.error(error);
      showToast(
        globalDispatch,
        error.message || "Error creating reservation",
        3000,
        "error"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const togglePlayer = (player) => {
    setSelectedPlayers((prev) => {
      const isSelected = prev.some((p) => p.id === player.id);
      if (isSelected) {
        return prev.filter((p) => p.id !== player.id);
      }
      return [...prev, player];
    });
  };

  const handleNextPlayers = () => {
    setCurrentView("players");
  };

  useEffect(() => {
    if (
      selectedPlayers?.length &&
      selectedSport &&
      selectedType &&
      selectedSubType &&
      selectedTimes?.length &&
      duration
    ) {
      const baseAmount = getCourtPrice({
        pricing,
        sportId: selectedSport,
        type: selectedType,
        subType: selectedSubType,
        duration: duration,
        selectedTime: selectedTimes[0], // Assuming single time slot selection
      });

      const serviceFee = calculateServiceFee(club?.fee_settings, baseAmount);
      setFee(baseAmount);
      setTotalFee(baseAmount + serviceFee);
    }
  }, [
    selectedPlayers,
    selectedSport,
    selectedType,
    selectedSubType,
    selectedTimes,
    pricing,
    club?.fee_settings,
  ]);

  const handleNextPayment = async () => {
    // Check if user has a subscription
    if (!user_subscription?.planId) {
      setWarningModal({
        isOpen: true,
        title: "Subscription Required",
        message: "Please subscribe to a membership plan to reserve courts",
        actionButtonText: "View Membership Plans",
        actionButtonLink: "/user/membership/buy",
        type: "warning",
      });
      return;
    }

    // Check if user's plan allows court reservations
    if (!user_permissions?.allowCourt) {
      setWarningModal({
        isOpen: true,
        title: "Plan Upgrade Required",
        message: `Your current plan (${user_permissions?.planName}) does not include court reservations. Please upgrade your plan.`,
        actionButtonText: "Upgrade Plan",
        actionButtonLink: "/user/membership/buy",
        type: "error",
      });
      return;
    }

    // Check if the selected date is still within the allowed range
    if (
      selectedDate > maxAllowedDate &&
      userMembershipPlan?.advance_booking_enabled?.court
    ) {
      const message = `Your membership plan only allows booking ${
        userMembershipPlan?.advance_booking_days?.court || 10
      } days in advance. Please select a valid date.`;

      setWarningModal({
        isOpen: true,
        title: "Date Selection Error",
        message: message,
        type: "warning",
      });
      setCurrentView("main");
      return;
    }

    // Check if sport has types and subtypes
    const sportHasTypes = selectedSportData?.sport_types?.some(
      (t) => t.type && t.type.trim() !== ""
    );
    const selectedTypeData = selectedSportData?.sport_types?.find(
      (t) => t.type === selectedType
    );
    const typeHasSubtypes = selectedTypeData?.subtype?.some(
      (st) => st && st.trim() !== ""
    );

    // Validate based on what the sport requires
    if (
      !selectedSport ||
      (sportHasTypes && !selectedType) ||
      (sportHasTypes && typeHasSubtypes && !selectedSubType) ||
      !selectedDate ||
      !selectedTimes.length
    ) {
      setWarningModal({
        isOpen: true,
        title: "Incomplete Details",
        message: "Please complete all required Reservation detail",
        type: "warning",
      });
      setCurrentView("main");
      return;
    }

    // Validate court selection if club allows user court selection
    if (club?.allow_user_court_selection === 1 && !selectedCourt) {
      setWarningModal({
        isOpen: true,
        title: "Court Selection Required",
        message: "Please select a court for your reservation",
        type: "warning",
      });
      // Don't send user back to step 1, keep them on the current step (players)
      return;
    }

    // Validate players selection
    if (!selectedPlayers.length) {
      setWarningModal({
        isOpen: true,
        title: "Players Required",
        message: "Please select at least one player",
        type: "warning",
      });
      return;
    }

    try {
      setPaymentIntentLoading(true);
      const booking_id = await handleCreateBooking();

      if (!booking_id) {
        throw new Error("Failed to create reservation");
      }

      setBookingId(booking_id);
      setCurrentView("payment");
    } catch (error) {
      console.error("Reservation error:", error);
      setWarningModal({
        isOpen: true,
        title: "Reservation Error",
        message: error.message || "Error creating reservation",
        type: "error",
      });
    } finally {
      setPaymentIntentLoading(false);
    }
  };

  const handleTimeClick = (time) => {
    // Add the new time range to selectedTimes
    setSelectedTimes([
      {
        from: time.from,
        until: time.until,
      },
    ]);
  };

  const reservationDescription = club?.court_description
    ? JSON.parse(club?.court_description)
    : {
        reservation_description: "",
        payment_description: "",
      };
  return (
    <div className="">
      <WarningModal
        isOpen={warningModal.isOpen}
        onClose={() => setWarningModal({ ...warningModal, isOpen: false })}
        title={warningModal.title}
        message={warningModal.message}
        actionButtonText={warningModal.actionButtonText}
        actionButtonLink={warningModal.actionButtonLink}
        type={warningModal.type}
      />
      {isLoading && <LoadingSpinner />}
      <div className="flex items-center justify-center bg-white p-4">
        {currentView === "main" && (
          <div className=" ">Step 1 • Select date and time</div>
        )}
        {currentView === "players" && (
          <div className=" ">Step 2 • Reservation detail</div>
        )}
        {currentView === "payment" && <div className=" ">Step 3 • Payment</div>}
      </div>
      <div className="p-4">
        <BackButton
          onBack={() => {
            if (currentView === "main") {
              navigate(-1);
            } else if (currentView === "payment") {
              setCurrentView("players");
            } else {
              setCurrentView("main");
            }
          }}
        />
        {currentView === "main" ? (
          <div className="p-4">
            <div className="space-y-6">
              <div className="mx-auto max-w-7xl p-4">
                <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
                  <SportTypeSelection
                    sports={sports}
                    onSelectionChange={({ sport, type, subType }) => {
                      setSelectedSport(sport);
                      setSelectedType(type);
                      setSelectedSubType(subType);
                      // Reset date, time, and court selections when sport changes
                      setSelectedDate(null);
                      setSelectedTimes([]);
                      setSelectedCourt(null);
                    }}
                  />

                  {selectedSport &&
                  (!selectedSportData?.sport_types?.length ||
                    (selectedType !== null &&
                      (selectedSubType !== null ||
                        !selectedSportData?.sport_types?.find(
                          (t) => t.type === selectedType
                        )?.subtype?.length))) ? (
                    <>
                      <div className="h-fit rounded-lg bg-white p-4 shadow-5">
                        {userMembershipPlan?.advance_booking_enabled?.court ? (
                          <div className="mb-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700">
                            You can reserve a court up to{" "}
                            {userMembershipPlan?.advance_booking_days?.court}{" "}
                            {userMembershipPlan?.advance_booking_days?.court ===
                            1
                              ? "day"
                              : "days"}{" "}
                            in advance.
                          </div>
                        ) : (
                          <div className="mb-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700">
                            You can reserve a court for any future date.
                          </div>
                        )}
                        <Calendar
                          currentMonth={currentMonth}
                          selectedDate={selectedDate}
                          onDateSelect={(date) => {
                            // Check if date is within allowed range
                            if (date > maxAllowedDate) {
                              const message = userMembershipPlan
                                ?.advance_booking_enabled?.court
                                ? `Your membership plan only allows booking ${
                                    userMembershipPlan?.advance_booking_days
                                      ?.court || 10
                                  } days in advance`
                                : ""; // No message needed when unlimited booking is allowed

                              if (message) {
                                showToast(
                                  globalDispatch,
                                  message,
                                  3000,
                                  "warning"
                                );
                                return;
                              }
                            }
                            setSelectedDate(date);
                          }}
                          onPreviousMonth={handlePreviousMonth}
                          onNextMonth={handleNextMonth}
                          daysOff={
                            club?.days_off ? JSON.parse(club.days_off) : []
                          }
                          allowPastDates={false}
                          minDate={new Date()}
                          maxDate={maxAllowedDate}
                          disabledDateMessage={
                            userMembershipPlan?.advance_booking_enabled?.court
                              ? `Your membership plan only allows booking ${
                                  userMembershipPlan?.advance_booking_days
                                    ?.court || 10
                                } days in advance`
                              : "You can book for any future date"
                          }
                        />
                      </div>

                      {selectedDate && (
                        <>
                          <TimeSlots
                            selectedDate={selectedDate}
                            timeRange={selectedTimes}
                            onTimeClick={handleTimeClick}
                            onNext={() => {
                              if (!selectedTimes.length) {
                                showToast(
                                  globalDispatch,
                                  "Please select a time slot",
                                  3000,
                                  "error"
                                );
                                return;
                              }

                              // Double-check that the selected date is within the allowed range
                              if (selectedDate > maxAllowedDate) {
                                const message = userMembershipPlan
                                  ?.advance_booking_enabled?.court
                                  ? `Your membership plan only allows booking ${
                                      userMembershipPlan?.advance_booking_days
                                        ?.court || 10
                                    } days in advance`
                                  : ""; // No message needed when unlimited booking is allowed

                                if (message) {
                                  showToast(
                                    globalDispatch,
                                    message,
                                    3000,
                                    "warning"
                                  );
                                  return;
                                }
                              }

                              handleNextPlayers();
                            }}
                            nextButtonText="Next: Players"
                            startHour={0}
                            endHour={24}
                            interval={30}
                            className="h-fit"
                            isTimeSlotAvailable={() => true}
                            clubTimes={
                              club?.times ? JSON.parse(club.times) : []
                            }
                          />
                        </>
                      )}
                    </>
                  ) : (
                    <div className="flex h-full items-center justify-center rounded-lg bg-white p-8 shadow-5 md:col-span-2">
                      <div className="text-center text-gray-500">
                        <svg
                          className="mx-auto h-12 w-12 text-gray-400"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                          aria-hidden="true"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                          />
                        </svg>
                        <h3 className="mt-2 text-sm font-medium text-gray-900">
                          Please select a sport
                        </h3>
                        <p className="mt-1 text-sm text-gray-500">
                          Choose a sport, type, and sub-type to view available
                          time slots
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        ) : currentView === "payment" ? (
          <div className="mx-auto max-w-6xl">
            <div className="rounded-xl bg-[#F17B2C] px-4 py-3 text-white">
              <div className="flex items-center gap-2">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                <span className="!text-sm">
                  Your session is reserved. You have 15 minutes to complete the
                  payment, otherwise the reservation will be canceled.
                </span>
              </div>
            </div>

            <div className="mt-6 grid grid-cols-1 gap-6 lg:grid-cols-2">
              <div className="space-y-6">
                <CourtReservationSummary
                  selectedSport={selectedSport}
                  sports={sports}
                  selectedType={selectedType}
                  selectedSubType={selectedSubType}
                  selectedDate={selectedDate}
                  selectedTimes={selectedTimes}
                  selectedCourt={
                    selectedCourt
                      ? filteredCourts.find(
                          (court) => court.id === selectedCourt
                        )
                      : null
                  }
                />
              </div>

              <div className="space-y-6">
                <div className="rounded-xl bg-white p-6 shadow-5">
                  <h2 className="mb-4 text-center text-lg font-medium">
                    Payment details
                  </h2>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-500">Club fee</span>
                      <span>{fCurrency(fee)}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-500">Service fee</span>
                      <span>
                        {fCurrency(
                          calculateServiceFee(club?.fee_settings, fee)
                        )}
                      </span>
                    </div>
                    <div className="flex items-center justify-between border-t pt-4">
                      <span className="font-medium">Total</span>
                      <span className="font-medium">{fCurrency(totalFee)}</span>
                    </div>
                    <div>
                      <CheckoutForm
                        user={user_profile}
                        bookingId={bookingId}
                        reservationId={reservationId}
                        clientSecret={clientSecret}
                        paymentIntent={paymentIntent}
                        navigateRoute={`/user/payment-success/${reservationId}?type=court`}
                      />
                      <div className="mt-4">
                        <div className="text-sm text-gray-500">
                          {reservationDescription?.payment_description}
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4 text-sm text-gray-500">
                      <p>
                        By clicking "Pay now" you agree to our{" "}
                        <Link
                          to="/terms-and-conditions"
                          target="_blank"
                          className="font-medium underline"
                        >
                          Terms and Conditions
                        </Link>{" "}
                        and{" "}
                        <Link
                          to="/privacy-policy"
                          target="_blank"
                          className="font-medium underline"
                        >
                          Privacy Policy
                        </Link>
                        . All sales are final unless stated otherwise.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="mx-auto grid max-w-7xl grid-cols-1 gap-4 md:grid-cols-3">
            <div className="space-y-4">
              {" "}
              <CourtReservationSummary
                selectedSport={selectedSport}
                sports={sports}
                selectedType={selectedType}
                selectedSubType={selectedSubType}
                selectedDate={selectedDate}
                selectedTimes={selectedTimes}
                selectedCourt={
                  selectedCourt
                    ? filteredCourts.find((court) => court.id === selectedCourt)
                    : null
                }
              />
              {/* Court Selection - only show if club allows user court selection */}
              {club?.allow_user_court_selection === 1 && (
                <div className="h-fit rounded-lg bg-white p-4 shadow-5">
                  <label className="mb-2 block text-sm font-medium text-gray-900">
                    Select Court
                  </label>
                  <Select
                    className="w-full text-sm"
                    options={filteredCourts.map((court) => ({
                      value: court.id,
                      label: court.name,
                    }))}
                    onChange={(option) =>
                      setSelectedCourt(option ? option.value : null)
                    }
                    value={
                      selectedCourt
                        ? {
                            value: selectedCourt,
                            label:
                              filteredCourts.find(
                                (court) => court.id === selectedCourt
                              )?.name || "Selected Court",
                          }
                        : null
                    }
                    isClearable={true}
                    placeholder="Select a court"
                    noOptionsMessage={() => {
                      if (!selectedSport) {
                        return "Please select a sport first";
                      }
                      if (
                        selectedSportData?.sport_types?.some(
                          (t) => t.type && t.type.trim() !== ""
                        ) &&
                        !selectedType
                      ) {
                        return "Please select a type";
                      }
                      const selectedTypeData =
                        selectedSportData?.sport_types?.find(
                          (t) => t.type === selectedType
                        );
                      if (
                        selectedTypeData?.subtype?.some(
                          (st) => st && st.trim() !== ""
                        ) &&
                        !selectedSubType
                      ) {
                        return "Please select a sub-type";
                      }
                      return "No courts available for the selected criteria";
                    }}
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Select your preferred court for this reservation
                  </p>
                </div>
              )}
            </div>

            <AddPlayers
              players={players}
              groups={groups}
              selectedPlayers={selectedPlayers}
              onPlayerToggle={(player) => {
                // Custom player toggle handler to show sport-specific message
                const isPlayerSelected = selectedPlayers.some(
                  (p) => p.id === player.id
                );

                // If player is already selected, check if it's the current user before allowing removal
                if (isPlayerSelected) {
                  if (player.id === user_profile?.id) {
                    showToast(
                      globalDispatch,
                      "You cannot remove yourself from the reservation",
                      3000,
                      "warning"
                    );
                    return;
                  }
                  togglePlayer(player);
                  return;
                }

                // If trying to add a player, check the limit
                if (selectedPlayers.length >= maxPlayersAllowed) {
                  // Get sport name for better error message
                  const sportName = selectedSportData?.name || "this sport";
                  showToast(
                    globalDispatch,
                    `Maximum ${maxPlayersAllowed} players allowed for ${sportName} (including yourself)`,
                    3000,
                    "warning"
                  );
                  return;
                }

                // If we get here, we can add the player
                togglePlayer(player);
              }}
              isFindBuddyEnabled={isFindBuddyEnabled}
              setSelectedPlayers={setSelectedPlayers}
              onFindBuddyToggle={() => {
                setIsFindBuddyEnabled(!isFindBuddyEnabled);
                setShowPlayersNeeded(!showPlayersNeeded);
              }}
              playersNeeded={playersNeeded}
              onPlayersNeededChange={setPlayersNeeded}
              maximumPlayers={maxPlayersAllowed}
              userProfile={user_profile}
              showPlayersNeeded={showPlayersNeeded}
              onNtrpMinChange={setNtrpMin}
              onNtrpMaxChange={setNtrpMax}
              onShortBioChange={setShortBio}
              initialNtrpMin={ntrpMin}
              initialNtrpMax={ntrpMax}
              initialShortBio={shortBio}
            />

            <div className="h-fit rounded-lg bg-white shadow-5">
              <div className="rounded-lg bg-gray-50 p-4 text-center">
                <h2 className="text-base font-medium">Reservation detail</h2>
              </div>
              <div className="p-4">
                <div className="space-y-6">
                  {/* Players count */}
                  <div>
                    <h3 className="text-sm text-gray-500">
                      PLAYERS ({selectedPlayers?.length})
                    </h3>
                    <div className="mt-1">
                      {selectedPlayers.length > 0 &&
                        selectedPlayers.map((player) => {
                          return (
                            <div key={player.id} className="text-sm">
                              {player.first_name} {player.last_name}
                            </div>
                          );
                        })}
                    </div>
                  </div>

                  {/* Fees section */}
                  <div>
                    <h3 className="text-sm text-gray-500">FEES</h3>
                    <div className="mt-2 flex items-center justify-between">
                      <span>Club Fee</span>
                      <span>{fCurrency(fee)}</span>
                    </div>
                    <div className="mt-2 flex items-center justify-between">
                      <span>Service Fee</span>
                      <span>
                        {fCurrency(
                          calculateServiceFee(club?.fee_settings, fee)
                        )}
                      </span>
                    </div>
                  </div>

                  {/* Total */}
                  <div className="flex items-center justify-between border-t pt-4">
                    <span>Total</span>
                    <span className="font-medium">{fCurrency(totalFee)}</span>
                  </div>

                  {/* Notification message */}
                  <div className="rounded-lg bg-[#F17B2C] p-3 text-sm text-white">
                    <div className="flex items-start gap-2">
                      <svg
                        width="16"
                        height="16"
                        viewBox="0 0 16 16"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M8.49364 2.62152L13.9236 12.1351C13.9737 12.2227 14 12.3221 14 12.4233C14 12.5245 13.9737 12.624 13.9236 12.7116C13.8736 12.7993 13.8017 12.8721 13.715 12.9227C13.6283 12.9733 13.5301 12.9999 13.43 12.9999H2.57C2.46995 12.9999 2.37165 12.9733 2.285 12.9227C2.19835 12.8721 2.12639 12.7993 2.07636 12.7116C2.02634 12.624 2 12.5245 2 12.4233C2 12.3221 2.02634 12.2227 2.07637 12.1351L7.50636 2.62152C7.5564 2.53387 7.62835 2.46109 7.715 2.41049C7.80165 2.35989 7.89995 2.33325 7.99995 2.33325C8.09995 2.33325 8.19835 2.35989 8.285 2.41049C8.37165 2.46109 8.4436 2.53387 8.49364 2.62152ZM7.42998 10.117V11.2702H8.57002V10.117H7.42998ZM7.42998 6.08098V8.96387H8.57002V6.08098H7.42998Z"
                          fill="white"
                        />
                      </svg>

                      <span>
                        After reserving, you will have 15 minutes to make the
                        payment.
                      </span>
                    </div>
                  </div>

                  {/* Make reservation button */}
                  <InteractiveButton
                    loading={paymentIntentLoading}
                    onClick={handleNextPayment}
                    className="w-full rounded-lg bg-[#1E2841] py-3 text-white hover:bg-[#1E2841]/90"
                  >
                    <div className="flex flex-col items-center">
                      <span>Reserve Now</span>
                      <span className="text-sm opacity-80">
                        and continue to payment
                      </span>
                    </div>
                  </InteractiveButton>

                  <div className="text-center text-sm text-gray-500">
                    {reservationDescription?.reservation_description}
                  </div>

                  {/* Additional note */}
                  <div className="space-y-2 text-center text-sm text-gray-500">
                    <p>(You will not be charged yet)</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default UserReserveCourt;
