import { useState, useEffect, useMemo } from "react";
import { format } from "date-fns";
import BottomDrawer from "Components/Drawers/BottomDrawer";
import PlayersSelectionDrawer from "./PlayersSelectionDrawer";
import TimeSlots from "Components/TimeSlots/TimeSlots";
import { Calendar } from "Components/Calendar";
import { useClub } from "Context/Club";
import { showToast, GlobalContext } from "Context/Global";
import { useContext } from "react";

export default function AvailabilityDrawer({
  isOpen,
  onClose,
  coach,
  players,
  selectedLocation,
  selectedSport,
  sports,
  groups,
  club,
  selectedType,
  selectedSubType,
  userProfile,
  coachAvailability,
}) {
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [timeRange, setTimeRange] = useState({ from: null, until: null });
  const [showPlayersSelection, setShowPlayersSelection] = useState(false);
  const [selectedTimes, setSelectedTimes] = useState([]);

  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const { club_membership, user_subscription } = useClub();

  // Get the user's membership plan
  const userMembershipPlan = useMemo(() => {
    if (!user_subscription?.planId || !club_membership?.length) return null;
    return club_membership.find(
      (plan) => plan.plan_id === user_subscription.planId
    );
  }, [user_subscription, club_membership]);

  // Calculate the maximum allowed date based on the user's membership plan
  const maxAllowedDate = useMemo(() => {
    // Check if advance booking is enabled for lessons
    if (userMembershipPlan?.advance_booking_enabled?.lesson === false) {
      // If advance booking is disabled, allow booking without limit
      const farFutureDate = new Date();
      farFutureDate.setFullYear(farFutureDate.getFullYear() + 10); // Set to 10 years in the future
      return farFutureDate;
    }

    // Default to 10 days in advance if no plan or advance booking days are specified
    const advanceDays = userMembershipPlan?.advance_booking_days?.lesson || 10;
    const today = new Date();
    const maxDate = new Date();
    maxDate.setDate(today.getDate() + advanceDays);
    return maxDate;
  }, [userMembershipPlan]);

  const isTimeSlotAvailable = (timeObj) => {
    if (!coach?.availability || !selectedDate) return false;

    const dayOfWeek = format(selectedDate, "EEEE").toLowerCase();
    // Convert availability object to array if it's not already
    const availabilityArray = Array.isArray(coach.availability)
      ? coach.availability
      : Object.entries(coach.availability).map(([day, timeslots]) => ({
          day,
          timeslots,
        }));

    const dayAvailability = availabilityArray.find(
      (avail) => avail.day === dayOfWeek
    );

    if (!dayAvailability) return false;

    // Convert time24 format (HH:mm) to match the availability format (HH:mm:00)
    const timeToCheck = `${timeObj.time24}:00`;
    return dayAvailability.timeslots.includes(timeToCheck);
  };

  // console.log("coach", coach);

  const handlePreviousMonth = () => {
    setCurrentMonth(
      new Date(currentMonth.setMonth(currentMonth.getMonth() - 1))
    );
  };

  const handleNextMonth = () => {
    setCurrentMonth(
      new Date(currentMonth.setMonth(currentMonth.getMonth() + 1))
    );
  };

  const handleNextPlayers = () => {
    // Double-check that the selected date is within the allowed range
    if (
      selectedDate > maxAllowedDate &&
      userMembershipPlan?.advance_booking_enabled?.lesson !== false
    ) {
      const advanceDays =
        userMembershipPlan?.advance_booking_days?.lesson || 10;
      showToast(
        globalDispatch,
        `Your membership plan only allows booking ${advanceDays} days in advance`,
        3000,
        "warning"
      );
      return;
    }

    setShowPlayersSelection(true);
  };

  const handlePlayersSelectionClose = () => {
    setShowPlayersSelection(false);
  };

  const handlePlayersSelectionNext = (data) => {
    console.log("Selected players:", data.selectedPlayers);
    // Handle the next step here
  };
  const handleTimeClick = (time) => {
    // Add the new time range to selectedTimes
    setSelectedTimes([
      {
        from: time.from,
        until: time.until,
      },
    ]);
  };

  // console.log("coachAvailability", coachAvailability);

  return (
    <>
      <BottomDrawer
        isOpen={isOpen && !showPlayersSelection}
        onClose={onClose}
        title={`${coach?.user?.first_name}'s availability`}
      >
        <div className="mx-auto max-w-3xl space-y-6 overflow-hidden">
          {/* Calendar Section */}
          <div className="grid grid-cols-2 gap-2">
            <div className="h-fit rounded-lg bg-white p-4 shadow-5">
              <div>
                {userMembershipPlan?.advance_booking_enabled?.lesson ===
                false ? (
                  <div className="mb-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700">
                    You can reserve a lesson for any future date.
                  </div>
                ) : (
                  <div className="mb-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700">
                    Your {userMembershipPlan?.plan_name || "current"} membership
                    plan allows you to book lessons up to{" "}
                    {userMembershipPlan?.advance_booking_days?.lesson || 10}{" "}
                    days in advance.
                  </div>
                )}
                <Calendar
                  currentMonth={currentMonth}
                  selectedDate={selectedDate}
                  onDateSelect={(date) => {
                    // Check if date is within allowed range
                    if (
                      date > maxAllowedDate &&
                      userMembershipPlan?.advance_booking_enabled?.lesson !==
                        false
                    ) {
                      const advanceDays =
                        userMembershipPlan?.advance_booking_days?.lesson || 10;
                      showToast(
                        globalDispatch,
                        `Your membership plan only allows booking ${advanceDays} days in advance`,
                        3000,
                        "warning"
                      );
                      return;
                    }
                    setSelectedDate(date);
                  }}
                  onPreviousMonth={handlePreviousMonth}
                  onNextMonth={handleNextMonth}
                  daysOff={club?.days_off ? JSON.parse(club.days_off) : []}
                  coachAvailability={coachAvailability}
                  allowPastDates={false}
                  minDate={new Date()}
                  maxDate={maxAllowedDate}
                  disabledDateMessage={
                    userMembershipPlan?.advance_booking_enabled?.lesson ===
                    false
                      ? "You can book for any future date"
                      : `Your membership plan only allows booking ${
                          userMembershipPlan?.advance_booking_days?.lesson || 10
                        } days in advance`
                  }
                />
              </div>
            </div>

            <TimeSlots
              selectedDate={selectedDate}
              onTimeClick={handleTimeClick}
              onNext={handleNextPlayers}
              nextButtonText="Next: Players"
              startHour={0}
              endHour={24}
              interval={30}
              isTimeSlotAvailable={isTimeSlotAvailable}
              timeRange={selectedTimes}
              clubTimes={club?.times ? JSON.parse(club.times) : []}
              coachAvailability={coachAvailability}
              height="h-full"
            />
          </div>
        </div>
      </BottomDrawer>

      <PlayersSelectionDrawer
        players={players}
        isOpen={showPlayersSelection}
        onClose={handlePlayersSelectionClose}
        selectedDate={selectedDate}
        timeRange={timeRange}
        onNext={handlePlayersSelectionNext}
        selectedSport={selectedSport}
        selectedLocation={selectedLocation}
        sports={sports}
        groups={groups}
        coach={coach}
        club={club}
        selectedTimes={selectedTimes}
        selectedType={selectedType}
        selectedSubType={selectedSubType}
        userProfile={userProfile}
      />
    </>
  );
}
