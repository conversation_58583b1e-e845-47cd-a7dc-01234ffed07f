import { useState, useEffect } from "react";
import { BiSearch } from "react-icons/bi";
import { BsArrowDownUp } from "react-icons/bs";
import { fCurrency } from "Utils/formatNumber";
import AvailabilityDrawer from "Components/Availability/AvailabilityDrawer";
import CoachProfile from "./CoachProfile";
import { useSearchParams } from "react-router-dom";
import SportTypeSelection from "Components/Shared/SportTypeSelection";
import MkdSDK from "Utils/MkdSDK";

let sdk = new MkdSDK();
export default function FindByCoachTab({
  sports,
  coaches,
  players,
  groups,
  club,
  userProfile,
}) {
  const [selectedSport, setSelectedSport] = useState("Tennis");
  const [selectedLocation, setSelectedLocation] = useState("Indoors");
  const [selectedCoach, setSelectedCoach] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [sortAscending, setSortAscending] = useState(true);
  const [showAvailability, setShowAvailability] = useState(false);
  const [selectedType, setSelectedType] = useState(null);
  const [selectedSubType, setSelectedSubType] = useState(null);

  const [loadingAvailability, setLoadingAvailability] = useState(false);
  const [coachAvailability, setCoachAvailability] = useState(null);

  const [params] = useSearchParams();
  const coachId = params.get("coach");

  useEffect(() => {
    if (coachId) {
      const coach = coaches.find((coach) => coach.id === parseInt(coachId));
      setSelectedCoach(coach);
    }
  }, [coachId, coaches]);

  // Get the selected sport's types and subtypes
  const selectedSportData = sports?.find((s) => s.id === selectedSport);

  const handleCheckAvailability = async () => {
    setLoadingAvailability(true);
    try {
      const response = await sdk.callRawAPI(
        `/v3/api/custom/courtmatchup/user/coach/availability/${selectedCoach.id}`,
        {},
        "GET"
      );
      setCoachAvailability(response.availability);
      setShowAvailability(true);
      console.log(response);
    } catch (error) {
      console.log(error);
    } finally {
      setLoadingAvailability(false);
    }
  };

  return (
    <>
      <div className="space-y-6">
        <div className="mx-auto max-w-7xl p-4">
          <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
            <SportTypeSelection
              sports={sports}
              onSelectionChange={({ sport, type, subType }) => {
                setSelectedSport(sport);
                setSelectedType(type);
                setSelectedSubType(subType);
              }}
            />

            {selectedSport &&
            (!selectedSportData?.sport_types?.length ||
              (selectedType !== null &&
                (selectedSubType !== null ||
                  !selectedSportData?.sport_types?.find(
                    (t) => t.type === selectedType
                  )?.subtype?.length))) ? (
              <div className="max-h-fit space-y-6 rounded-xl bg-white p-4 shadow-5">
                {/* Search Bar */}
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Search by name"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full rounded-lg border border-gray-300 py-2 pl-10 pr-4 focus:border-blue-500 focus:outline-none"
                  />
                  <BiSearch className="absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400" />
                  <button
                    onClick={() => setSortAscending(!sortAscending)}
                    className="absolute right-2 top-1/2 -translate-y-1/2 transform rounded-md border border-gray-300 px-2 py-1 text-sm hover:bg-gray-50"
                  >
                    <div className="flex items-center gap-1">
                      <span>A-Z</span>
                      <BsArrowDownUp
                        className={`text-xs transition-transform ${
                          !sortAscending ? "rotate-180" : ""
                        }`}
                      />
                    </div>
                  </button>
                </div>

                {/* Coach Listings */}
                <div className="max-h-[400px] space-y-4 overflow-y-auto">
                  {coaches.length > 0 &&
                    coaches
                      .filter((coach) => {
                        const fullName =
                          `${coach.user?.first_name} ${coach.user?.last_name}`.toLowerCase();
                        return fullName.includes(searchQuery.toLowerCase());
                      })
                      .sort((a, b) => {
                        const nameA =
                          `${a.user?.first_name} ${a.user?.last_name}`.toLowerCase();
                        const nameB =
                          `${b.user?.first_name} ${b.user?.last_name}`.toLowerCase();
                        return sortAscending
                          ? nameA.localeCompare(nameB)
                          : nameB.localeCompare(nameA);
                      })
                      .map((coach) => (
                        <div
                          key={coach.id}
                          className={`flex cursor-pointer items-center justify-between rounded-lg border p-3 ${
                            selectedCoach?.id === coach.id
                              ? "border-primaryBlue bg-blue-50"
                              : "border-gray-100 hover:bg-gray-50"
                          }`}
                          onClick={() => setSelectedCoach(coach)}
                        >
                          <div className="flex items-center gap-3">
                            <img
                              src={
                                coach.user?.photo ||
                                coach?.photo ||
                                `/default-avatar.png`
                              }
                              alt={`${coach.user?.first_name} ${coach.user?.last_name}`}
                              className="h-10 w-10 rounded-full object-cover"
                            />
                            <div className="flex flex-col">
                              <span className="font-medium capitalize">
                                {coach.user?.first_name} {coach.user?.last_name}
                              </span>
                            </div>
                          </div>
                          <span className="text-gray-600">
                            {fCurrency(coach.hourly_rate)}/h
                          </span>
                        </div>
                      ))}
                  {coaches.length === 0 && (
                    <p className="text-center text-sm text-gray-500">
                      No coaches found
                    </p>
                  )}
                </div>
              </div>
            ) : (
              <div className="col-span-2 flex h-full items-center justify-center rounded-lg bg-white p-8 shadow-5">
                <div className="text-center text-gray-500">
                  <svg
                    className="mx-auto h-12 w-12 text-gray-400"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    aria-hidden="true"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                    />
                  </svg>
                  <h3 className="mt-2 text-sm font-medium text-gray-900">
                    Please select a sport
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Choose a sport, type, and sub-type to view available coaches
                  </p>
                </div>
              </div>
            )}

            <CoachProfile
              selectedCoach={selectedCoach}
              onCheckAvailability={handleCheckAvailability}
              loadingAvailability={loadingAvailability}
            />
          </div>
        </div>
      </div>

      <AvailabilityDrawer
        isOpen={showAvailability}
        coachAvailability={coachAvailability}
        onClose={() => setShowAvailability(false)}
        selectedSport={selectedSport}
        selectedLocation={selectedLocation}
        sports={sports}
        groups={groups}
        club={club}
        players={players}
        selectedType={selectedType}
        selectedSubType={selectedSubType}
        userProfile={userProfile}
        coach={{
          ...selectedCoach,
          availability: JSON.parse(selectedCoach?.availability || "{}"),
        }}
      />
    </>
  );
}
